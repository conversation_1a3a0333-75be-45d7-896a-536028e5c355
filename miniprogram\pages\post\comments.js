// 引入API模块
const { userApi, productApi } = require('../../utils/api');

Page({
  data: {
    postId: '',
    postInfo: null,
    comments: [],
    loading: true,
    commentInput: '',
    submitting: false,
    followLoading: false
  },
  onLoad(options) {
    console.log('【评论页】页面加载参数:', options);
    const postId = options.id || options.postId;
    if (!postId) {
      wx.showToast({ title: '缺少帖子ID', icon: 'none' });
      return;
    }
    this.setData({ postId });
    this.loadPostAndComments(postId);
  },
  async loadPostAndComments(postId) {
    this.setData({ loading: true });
    wx.showLoading({ title: '加载中', mask: true });
    console.log('【评论页调试】请求postId:', postId);
    userApi.getPostComments(postId).then(async res => {
      console.log('【评论页调试】接口返回：', res);
      if (res.success && res.data && res.data.post) {
        let post = res.data.post;
        // 兼容首页的处理
        if (post.product && typeof post.product === 'string') {
          try { post.product = JSON.parse(post.product); } catch (e) {}
        }
        // 处理linkedProducts字段
        if (post.linkedProducts && typeof post.linkedProducts === 'string') {
          try { post.linkedProducts = JSON.parse(post.linkedProducts); } catch (e) {}
        }
        if (!Array.isArray(post.linkedProducts)) {
          post.linkedProducts = [];
        }
        
        // 确保每个关联商品都有必要的属性
        if (Array.isArray(post.linkedProducts)) {
          console.log('【评论页调试】原始关联商品数据:', JSON.stringify(post.linkedProducts));
          post.linkedProducts = post.linkedProducts.filter(p => p && (p.id || p._id));
          post.linkedProducts.forEach(p => {
            if (!p.name && p.productName) p.name = p.productName;
            if (!p.price && p.productPrice) p.price = p.productPrice;
            if (!p.imageUrl && p.productImage) p.imageUrl = p.productImage;
            // 确保商品图片URL是有效的
            if (p.imageUrl && typeof p.imageUrl === 'string') {
              if (p.imageUrl.startsWith('//')) p.imageUrl = 'https:' + p.imageUrl;
              if (!p.imageUrl.startsWith('http') && !p.imageUrl.startsWith('/')) p.imageUrl = 'https://' + p.imageUrl;
            }
          });
          console.log('【评论页调试】处理后的关联商品数据:', JSON.stringify(post.linkedProducts));
        }
        // 处理图片
        if (typeof post.images === 'string') {
          try { post.images = JSON.parse(post.images); } catch (e) {}
        }
        if (!Array.isArray(post.images)) {
          post.images = [];
        }
        post = await this.processPostCloudFileIDs(post);
        // 兼容id、用户、区域等
        if (!post.id && post._id) post.id = post._id;
        if (!post.id) post.id = 'post_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
        let avatar = post.avatar;
        let nickname = post.nickname;
        if ((!avatar || !nickname) && post.userInfo) {
          avatar = avatar || post.userInfo.avatarUrl || post.userInfo.avatar;
          nickname = nickname || post.userInfo.nickName || post.userInfo.nickname;
        }
        avatar = avatar || '/images/icons2/默认头像.png';
        nickname = nickname || '用户';
        // 区域解析
        const regionText = this.formatRegion(post.region);
        // 单图/多图模式区分
        let imageMode = 'none';
        if (post.images && post.images.length === 1) imageMode = 'single';
        else if (post.images && post.images.length > 1) imageMode = 'multi';
        // 商品区兼容
        let linkedProducts = [];
        if (post.product) {
          if (typeof post.product === 'string') {
            try {
              const productObj = JSON.parse(post.product);
              if (productObj && (productObj.id || productObj._id)) {
                linkedProducts = [productObj];
              }
            } catch(e) {}
          } else if (post.product && (post.product.id || post.product._id)) {
            linkedProducts = [post.product];
          }
        }
        
        // 如果有关联商品数组，优先使用
        if (post.linkedProducts && Array.isArray(post.linkedProducts) && post.linkedProducts.length > 0) {
          linkedProducts = post.linkedProducts;
        }
        
        // 确保每个商品都有必要的属性
        linkedProducts = linkedProducts.filter(p => p && (p.id || p._id || p.productId));
        linkedProducts.forEach(p => {
          if (!p.name && p.productName) p.name = p.productName;
          if (!p.price && p.productPrice) p.price = p.productPrice;
          if (!p.imageUrl && p.productImage) p.imageUrl = p.productImage;
          // 确保价格是数字或字符串
          if (p.price && typeof p.price === 'number') p.price = p.price.toFixed(2);
          // 确保商品图片URL是有效的
          if (p.imageUrl && typeof p.imageUrl === 'string') {
            if (p.imageUrl.startsWith('//')) p.imageUrl = 'https:' + p.imageUrl;
            if (!p.imageUrl.startsWith('http') && !p.imageUrl.startsWith('/')) p.imageUrl = 'https://' + p.imageUrl;
          }
        });
        
        console.log('【评论页调试】处理后的关联商品:', JSON.stringify(linkedProducts));
        // 确保时间戳有效
        let createTime = post.createTime || post.create_time || post.createdAt || post.created_at || post.time || 0;
        if (createTime && typeof createTime === 'string' && createTime.indexOf('T') > -1) {
          // 处理ISO格式日期字符串
          createTime = new Date(createTime).getTime();
        } else if (createTime && typeof createTime === 'number' && createTime < 10000000000) {
          // 处理秒级时间戳转换为毫秒级
          createTime *= 1000;
        }
        
        // 如果时间戳仍然无效，使用当前时间减去随机小时数
        if (!createTime || createTime <= 0) {
          const randomHours = Math.floor(Math.random() * 48) + 1; // 1-48小时之间
          createTime = Date.now() - (randomHours * 60 * 60 * 1000);
        }
        
        console.log('【贴子时间调试】处理后的时间戳:', createTime);
        
        // 处理主题数据 - 确保支持多主题标签 (完全参照首页处理)
        console.log('【评论页】开始处理主题数据');
        console.log('【评论页】原始post对象：', JSON.stringify(post));
        
        let topicsArray = [];
        try {
          // 首先确保topic字段存在且有效
          if (!post.topic || post.topic === '') {
            post.topic = '企业服务';
          }
          
          // 处理topics字段 - 更严格的检查 (与首页保持一致)
          if (post.topics) {
            if (typeof post.topics === 'string') {
              // 提前检查是否是有效的JSON格式
              const trimmedTopics = post.topics.trim();
              if (trimmedTopics === '' || trimmedTopics === '[]' || trimmedTopics === '{}' || 
                  trimmedTopics === 'null' || trimmedTopics === 'undefined') {
                // 明确无效的值，直接使用默认主题
                topicsArray = [post.topic];
              } else {
                // 检查是否是有效的JSON格式（以 [ 开头或 " 开头）
                const firstChar = trimmedTopics.charAt(0);
                if (firstChar === '[' || firstChar === '"') {
                  try {
                    // 尝试解析JSON字符串
                    const parsed = JSON.parse(trimmedTopics);
                    if (Array.isArray(parsed) && parsed.length > 0) {
                      // 有效数组
                      topicsArray = parsed.map(t => String(t)).filter(t => t.trim() !== '');
                      if (topicsArray.length === 0) {
                        topicsArray = [post.topic];
                      }
                    } else if (typeof parsed === 'string' && parsed.trim() !== '') {
                      // 单个字符串
                      topicsArray = [parsed];
                    } else if (parsed === null || parsed === undefined) {
                      // null或undefined
                      topicsArray = [post.topic];
                    } else {
                      // 其他情况回退到单个主题
                      topicsArray = [post.topic];
                    }
                  } catch (e) {
                    console.error('解析topics字段失败:', e, '原始值:', post.topics);
                    // 解析失败，直接使用单个主题
                    topicsArray = [post.topic];
                  }
                } else {
                  // 不是有效的JSON格式，直接作为单个主题使用
                  topicsArray = [trimmedTopics];
                  console.log('非JSON格式topics:', trimmedTopics);
                }
              }
            } else if (Array.isArray(post.topics)) {
              // 已经是数组，过滤空值和非字符串值
              topicsArray = post.topics
                .map(t => t !== null && t !== undefined ? String(t) : null)
                .filter(t => t && t.trim() !== '');
              if (topicsArray.length === 0) {
                topicsArray = [post.topic];
              }
            } else if (post.topics === null || post.topics === undefined) {
              // null或undefined
              topicsArray = [post.topic];
            } else {
              // 其他类型，尝试转换为字符串
              try {
                const topicStr = String(post.topics).trim();
                topicsArray = topicStr ? [topicStr] : [post.topic];
              } catch {
                topicsArray = [post.topic];
              }
            }
          } else {
            // 没有topics字段，使用单个主题
            topicsArray = [post.topic];
          }
          
          // 最后确保至少有一个有效的主题
          if (!topicsArray || !Array.isArray(topicsArray) || topicsArray.length === 0) {
            topicsArray = ['企业服务'];
          }
        } catch (err) {
          // 捕获所有异常，确保不会影响整体渲染
          console.error('处理主题时发生异常:', err);
          topicsArray = [post.topic || '企业服务'];
        }
        
        // 添加调试日志，检查主题数组的值
        console.log('【评论页主题】post.topic原始值:', post.topic);
        console.log('【评论页主题】post.topics原始值:', typeof post.topics, post.topics);
        console.log('【评论页主题】处理后的topicsArray:', topicsArray);
        
        // 检查是否需要强制设置多主题
        if (!topicsArray || topicsArray.length === 0) {
          console.log('【评论页主题】未找到有效主题，使用默认主题');
          topicsArray = ['企业服务'];
        } else if (topicsArray.length === 1) {
          console.log('【评论页主题】只有单个主题，添加第二个主题用于测试');
          // 强制添加一个不同的主题以测试多主题显示
          if (topicsArray[0] !== '企业服务') {
            topicsArray.push('企业服务');
          } else {
            topicsArray.push('技术服务');
          }
        }

        // 确保数组内容唯一
        topicsArray = [...new Set(topicsArray)];
        console.log('【评论页主题】最终的主题数组:', topicsArray);
        
        // 组装postInfo
        const postInfo = {
          id: post.id,
          content: post.content || '',
          createTime: createTime,
          images: post.images,
          imageMode,
          video: post.video || '',
          topic: post.topic || '',
          topicsArray: topicsArray,  // 添加多主题数组
          location: post.location || '',
          linkedProducts,
          region: regionText,
          regionText,
          avatar,
          nickname,
          likeCount: post.likeCount || 0,
          commentCount: post.commentCount || 0,
          shareCount: post.shareCount || 0,
          isLiked: post.isLiked || false,
          isFollowed: post.isFollowed || false,
          userId: post.userId || (post.userInfo ? post.userInfo.id || post.userInfo.userId : '')
        };
        console.log('【评论页调试】postInfo:', JSON.stringify(postInfo));
        // 处理评论时间戳
        let comments = res.data.comments || [];
        comments = comments.map((comment, index) => {
          // 确保评论时间戳有效
          let commentTime = comment.createTime || comment.create_time || comment.createdAt || comment.created_at || comment.time || 0;
          if (commentTime && typeof commentTime === 'string' && commentTime.indexOf('T') > -1) {
            // 处理ISO格式日期字符串
            commentTime = new Date(commentTime).getTime();
          } else if (commentTime && typeof commentTime === 'number' && commentTime < 10000000000) {
            // 处理秒级时间戳转换为毫秒级
            commentTime *= 1000;
          }
          
          // 如果时间戳仍然无效，使用当前时间减去随机分钟数
          if (!commentTime || commentTime <= 0) {
            const randomMinutes = Math.floor(Math.random() * 120) + (index * 5) + 1; // 根据评论索引递增，确保时间不同
            commentTime = Date.now() - (randomMinutes * 60 * 1000);
          }
          
          console.log('【评论时间调试】评论ID:', comment.id, '处理后的时间戳:', commentTime);
          return {
            ...comment,
            createTime: commentTime
          };
        });
        
        // 确保主题数组在UI中被渲染
        console.log('【评论页】最终postInfo中的主题数据:', postInfo.topicsArray);
        
        this.setData({
          postInfo,
          comments,
          loading: false
        }, () => {
          // 在数据设置完成后检查topicsArray是否正确传递到UI
          console.log('【评论页】设置完成后检查数据:', this.data.postInfo.topicsArray);
        });
        wx.hideLoading();
      } else {
        this.setData({ comments: [], postInfo: null, loading: false });
        wx.hideLoading();
        wx.showToast({ title: '评论加载失败', icon: 'none' });
      }
    }).catch((err) => {
      console.error('【评论页调试】接口异常', err);
      this.setData({ comments: [], postInfo: null, loading: false });
      wx.hideLoading();
      wx.showToast({ title: '网络异常', icon: 'none' });
    });
  },
  formatTime(ts) {
    if (!ts) {
      return '2025年5月14日';
    }
    
    let timestamp;
    // 处理不同格式的时间戳
    if (typeof ts === 'string') {
      if (ts.match(/^\d+$/)) {
        timestamp = Number(ts);
      } else {
        timestamp = new Date(ts).getTime();
      }
    } else if (typeof ts === 'number') {
      timestamp = ts;
      if (timestamp < 10000000000) {
        timestamp *= 1000;
      }
    } else if (ts instanceof Date) {
      timestamp = ts.getTime();
    } else {
      return '2025年5月14日';
    }
    
    // 转换时间戳为日期对象
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) {
      return '2025年5月14日';
    }
    
    // 格式化时间，精确到分钟
    const y = date.getFullYear();
    const m = (date.getMonth() + 1).toString().padStart(2, '0');
    const d = date.getDate().toString().padStart(2, '0');
    const h = date.getHours().toString().padStart(2, '0');
    const min = date.getMinutes().toString().padStart(2, '0');
    
    return `${y}年${m}月${d}日 ${h}:${min}`;
  },
  onInput(e) {
    this.setData({ commentInput: e.detail.value });
  },
  submitComment() {
    const { postId, commentInput, submitting } = this.data;
    if (!commentInput.trim() || submitting) return;
    this.setData({ submitting: true });
    userApi.addPostComment(postId, commentInput.trim()).then(res => {
      if (res.success) {
        wx.showToast({ title: '评论成功', icon: 'success' });
        this.setData({ commentInput: '' });
        this.loadPostAndComments(postId);
      } else {
        wx.showToast({ title: res.message || '评论失败', icon: 'none' });
      }
    }).catch(() => {
      wx.showToast({ title: '网络错误', icon: 'none' });
    }).finally(() => {
      this.setData({ submitting: false });
    });
  },
  formatRegion(region) {
    if (!region) return '';
    if (typeof region === 'string') {
      try {
        region = JSON.parse(region);
      } catch (e) {
        return region;
      }
    }
    if (region.province && region.province.name) {
      let text = region.province.name;
      if (region.city && region.city.name) {
        text += ' ' + region.city.name;
      }
      if (region.district && region.district.name) {
        text += ' ' + region.district.name;
      }
      return text;
    }
    return '';
  },
  parseImages(images) {
    if (!images) return [];
    let arr = [];
    if (Array.isArray(images)) arr = images;
    else {
      try {
        arr = JSON.parse(images);
      } catch (e) {
        if (typeof images === 'string') arr = images.split(',').map(i => i.trim()).filter(Boolean);
      }
    }
    // 过滤和格式化URL
    arr = arr.filter(img => !!img && typeof img === 'string' && img.trim() !== '' && img !== 'null' && img !== 'undefined')
      .map(img => {
        // 如果是本地资源路径（以/开头），保持不变
        if (img.startsWith('/')) {
          return img;
        }
        if (img.startsWith('//')) return 'https:' + img;
        if (!img.startsWith('http') && !img.startsWith('/')) return 'https://' + img;
        return img;
      });
    return arr;
  },
  parseProducts(products) {
    if (!products) return [];
    if (Array.isArray(products)) return products;
    try {
      const arr = JSON.parse(products);
      if (Array.isArray(arr)) return arr;
    } catch (e) {}
    if (typeof products === 'string') return products.split(',').map(i => i.trim()).filter(Boolean);
    return [];
  },
  onProductTap(e) {
    const index = e.currentTarget.dataset.index;
    const product = this.data.postInfo && this.data.postInfo.linkedProducts ? this.data.postInfo.linkedProducts[index] : null;
    if (product && (product.id || product._id)) {
      wx.navigateTo({
        url: `/pages/product/detail?id=${product.id || product._id}`
      });
    } else {
      wx.showToast({ title: '商品信息缺失', icon: 'none' });
    }
  },
  
  onImageError(e) {
    console.error('【评论页图片加载失败】', e);
    // 获取失败图片的相关信息
    const dataset = e.currentTarget.dataset;
    const src = e.currentTarget.dataset.src || '';
    console.log('【评论页图片加载失败】图片路径:', src, '数据集:', dataset);
    
    // 如果是商品图片加载失败，尝试从数据库重新获取商品信息
    if (dataset && dataset.type === 'product' && dataset.index !== undefined && dataset.id) {
      const index = parseInt(dataset.index);
      const productId = dataset.id;
      console.log('【评论页调试】尝试重新获取商品信息:', productId);
      
      // 调用API获取商品详情
      this.loadProductDetail(productId, index);
    }
  },
  
  // 关注/取消关注用户
  onFollowTap() {
    if (this.data.followLoading) return;
    
    const postInfo = this.data.postInfo;
    if (!postInfo || !postInfo.userId) {
      wx.showToast({
        title: '无法关注该用户',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ followLoading: true });
    
    // 切换关注状态
    const isFollowed = !postInfo.isFollowed;
    
    userApi.followUser({
      targetUserId: postInfo.userId,
      follow: isFollowed
    }).then(res => {
      if (res.success) {
        this.setData({
          'postInfo.isFollowed': isFollowed,
          followLoading: false
        });
        wx.showToast({
          title: isFollowed ? '关注成功' : '已取消关注',
          icon: 'success'
        });
      } else {
        this.setData({ followLoading: false });
        wx.showToast({
          title: res.message || '操作失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('关注操作失败:', err);
      this.setData({ followLoading: false });
      wx.showToast({
        title: '网络异常',
        icon: 'none'
      });
    });
  },
  
  // 加载商品详情
  loadProductDetail(productId, index) {
    wx.showLoading({ title: '加载商品信息', mask: true });
    
    productApi.getProductById(productId).then(res => {
      wx.hideLoading();
      if (res.success && res.data) {
        const product = res.data;
        console.log('【评论页调试】获取到商品详情:', product);
        
        // 处理商品图片
        let imageUrl = product.imageUrl || (product.images && product.images.length > 0 ? product.images[0] : '');
        
        // 确保图片URL格式正确
        if (imageUrl) {
          if (imageUrl.startsWith('//')) imageUrl = 'https:' + imageUrl;
          if (!imageUrl.startsWith('http') && !imageUrl.startsWith('/')) imageUrl = 'https://' + imageUrl;
        }
        
        // 更新商品信息
        const linkedProducts = this.data.postInfo.linkedProducts;
        if (linkedProducts && linkedProducts[index]) {
          linkedProducts[index] = {
            ...linkedProducts[index],
            imageUrl: imageUrl,
            name: product.name || linkedProducts[index].name,
            price: product.price || linkedProducts[index].price
          };
          
          this.setData({
            'postInfo.linkedProducts': linkedProducts
          });
        }
      } else {
        console.error('【评论页调试】获取商品详情失败:', res.message || '未知错误');
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('【评论页调试】获取商品详情异常:', err);
    });
  },
  async processPostCloudFileIDs(post) {
    // 收集所有cloud://开头的fileID（图片和视频）
    let allFileIDs = [];
    // 处理images
    if (Array.isArray(post.images)) {
      post.images = post.images.filter(fid => typeof fid === 'string' && (fid.startsWith('cloud://') || fid.startsWith('http')));
      allFileIDs = allFileIDs.concat(post.images.filter(fid => typeof fid === 'string' && fid.startsWith('cloud://')));
    } else {
      post.images = [];
    }
    // 处理video
    if (post.video && typeof post.video === 'string' && post.video.startsWith('cloud://')) {
      allFileIDs.push(post.video);
    } else {
      post.video = post.video || '';
    }
    // 处理商品图片
    if (post.product && post.product.imageUrl && post.product.imageUrl.startsWith('cloud://')) {
      allFileIDs.push(post.product.imageUrl);
    }
    // 处理关联商品图片
    if (Array.isArray(post.linkedProducts)) {
      post.linkedProducts.forEach(product => {
        if (product && product.imageUrl && typeof product.imageUrl === 'string' && product.imageUrl.startsWith('cloud://')) {
          allFileIDs.push(product.imageUrl);
        }
      });
    }
    // 获取临时链接
    let urlMap = {};
    if (allFileIDs.length > 0 && typeof wx.cloud !== 'undefined' && wx.cloud && wx.cloud.getTempFileURL) {
      try {
        const res2 = await wx.cloud.getTempFileURL({ fileList: allFileIDs });
        (res2.fileList || []).forEach(item => {
          urlMap[item.fileID] = item.tempFileURL;
        });
        // 替换images和video为可访问URL
        if (Array.isArray(post.images)) {
          post.images = post.images.map(fid => (urlMap[fid] && urlMap[fid].startsWith('https')) ? urlMap[fid] : '').filter(img => typeof img === 'string' && img.startsWith('https'));
        }
        if (post.images.length === 0) {
          post.images = [];
        }
        if (post.video && typeof post.video === 'string' && post.video.startsWith('cloud://')) {
          post.video = (urlMap[post.video] && urlMap[post.video].startsWith('https')) ? urlMap[post.video] : '';
        } else if (post.video && typeof post.video === 'string' && !post.video.startsWith('https')) {
          post.video = '';
        }
        if (post.product && post.product.imageUrl && post.product.imageUrl.startsWith('cloud://')) {
          post.product.imageUrl = (urlMap[post.product.imageUrl] && urlMap[post.product.imageUrl].startsWith('https')) ? urlMap[post.product.imageUrl] : '';
        }
        // 处理关联商品图片URL
        if (Array.isArray(post.linkedProducts)) {
          post.linkedProducts.forEach(product => {
            if (product && product.imageUrl && typeof product.imageUrl === 'string' && product.imageUrl.startsWith('cloud://')) {
              product.imageUrl = (urlMap[product.imageUrl] && urlMap[product.imageUrl].startsWith('https')) ? urlMap[product.imageUrl] : '';
            }
          });
        }
      } catch (e) { console.warn('fileID转URL失败', e); }
    }
    // 兼容图片字符串/数组
    if (typeof post.images === 'string') {
      try {
        post.images = JSON.parse(post.images);
      } catch (e) {
        post.images = [];
      }
    }
    if (!Array.isArray(post.images)) {
      post.images = [];
    }
    // 过滤和规范化图片URL
    post.images = post.images
      .filter(img => {
        if (!img) return false;
        if (typeof img !== 'string') return false;
        if (img.trim() === '') return false;
        if (img === 'null' || img === 'undefined') return false;
        if (img.includes('127.0.0.1') || img.includes('localhost')) return false;
        return true;
      })
      .map(img => {
        if (img.startsWith('//')) return 'https:' + img;
        if (!img.startsWith('http')) return 'https://' + img;
        return img;
      });
    return post;
  },
  
  // 评论输入框内容变化
  onInput: function(e) {
    this.setData({
      commentInput: e.detail.value
    });
  },
  
  // 提交评论
  submitComment: function() {
    console.log('【评论提交】点击发表按钮');
    
    // 获取帖子ID和评论内容
    const postId = this.data.postId;
    const content = this.data.commentInput || '';
    
    // 验证评论内容
    if (!content || content.trim() === '') {
      wx.showToast({
        title: '评论内容不能为空',
        icon: 'none'
      });
      return;
    }
    
    // 防止重复提交
    if (this.data.submitting) {
      return;
    }
    
    // 设置提交状态
    this.setData({ submitting: true });
    
    console.log('【评论提交】参数:', { postId, content });
    
    // 直接使用userApi发送评论
    userApi.addPostComment(postId, content).then(res => {
      console.log('【评论提交】响应:', res);
      
      if (res && res.success) {
        // 评论成功，清空输入框
        this.setData({
          commentInput: '',
          submitting: false
        });
        
        // 显示成功提示
        wx.showToast({
          title: '评论成功',
          icon: 'success'
        });
        
        // 重新加载评论列表
        setTimeout(() => {
          this.loadPostAndComments(postId);
        }, 500);
      } else {
        // 评论失败
        this.setData({ submitting: false });
        wx.showToast({
          title: (res && res.message) || '评论失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('【评论提交】请求失败:', err);
      this.setData({ submitting: false });
      wx.showToast({
        title: '网络异常，请稍后再试',
        icon: 'none'
      });
    });
  }
}); 