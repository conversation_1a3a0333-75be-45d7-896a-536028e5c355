const { userApi } = require('../../utils/api');

Page({
  data: {
    commentList: [],
    loading: true
  },
  onLoad() {
    this.loadMyComments();
  },
  loadMyComments() {
    this.setData({ loading: true });
    userApi.getMyComments().then(res => {
      if (res.success && Array.isArray(res.data)) {
        // 处理每个评论项的数据
        const processedData = res.data.map(item => {
          // 确保postInfo对象存在
          item.postInfo = item.postInfo || {};

          // 处理评论数据
          const comment = {
            commentId: item.commentId,
            postId: item.postId,
            myComment: item.myComment,
            commentTime: item.commentTime,
            postContent: item.postContent,
            postInfo: {
              avatar: item.postInfo.avatar || '/images/icons2/男头像.png',
              nickname: item.postInfo.nickname || '用户',
              createTime: item.postInfo.createTime,
              commentCount: item.postInfo.commentCount || 0,
              likeCount: item.postInfo.likeCount || 0,
              shareCount: item.postInfo.shareCount || 0
            },
            otherComments: item.otherComments || []
          };

          // 处理显示时间 - 确保时间戳有效
          let commentTime = comment.commentTime;
          let postTime = comment.postInfo.createTime;

          // 验证时间戳有效性
          if (!commentTime || commentTime <= 0) {
            commentTime = Date.now();
          }
          if (!postTime || postTime <= 0) {
            postTime = Date.now();
          }

          comment.formattedCommentTime = this.formatTime(commentTime);
          comment.formattedPostTime = this.formatTime(postTime);

          // 处理帖子主题标签
          if (comment.postInfo) {
            comment.postInfo = this.processTopics(comment.postInfo);
          }

          return comment;
        });
        this.setData({ commentList: processedData, loading: false });
        console.log('【我的评论页】处理后的评论列表：', processedData);
      } else {
        this.setData({ commentList: [], loading: false });
      }
    }).catch(() => {
      this.setData({ commentList: [], loading: false });
    });
  },

  // 处理主题标签的方法
  processTopics(post) {
    if (!post) return post;

    console.log('【我的评论页】处理帖子主题：', post);

    let topicsArray = [];
    try {
      // 首先确保topic字段存在且有效
      if (!post.topic || post.topic === '') {
        post.topic = '企业服务';
      }

      // 处理topics字段 - 更严格的检查
      if (post.topics) {
        if (typeof post.topics === 'string') {
          // 提前检查是否是有效的JSON格式
          const trimmedTopics = post.topics.trim();
          if (trimmedTopics === '' || trimmedTopics === '[]' || trimmedTopics === '{}' ||
              trimmedTopics === 'null' || trimmedTopics === 'undefined') {
            // 明确无效的值，直接使用默认主题
            topicsArray = [post.topic];
          } else {
            // 检查是否是有效的JSON格式（以 [ 开头或 " 开头）
            const firstChar = trimmedTopics.charAt(0);
            if (firstChar === '[' || firstChar === '"') {
              try {
                // 尝试解析JSON字符串
                const parsed = JSON.parse(trimmedTopics);
                if (Array.isArray(parsed) && parsed.length > 0) {
                  // 有效数组
                  topicsArray = parsed.map(t => String(t)).filter(t => t.trim() !== '');
                  if (topicsArray.length === 0) {
                    topicsArray = [post.topic];
                  }
                } else if (typeof parsed === 'string' && parsed.trim() !== '') {
                  // 单个字符串
                  topicsArray = [parsed];
                } else if (parsed === null || parsed === undefined) {
                  // null或undefined
                  topicsArray = [post.topic];
                } else {
                  // 其他情况回退到单个主题
                  topicsArray = [post.topic];
                }
              } catch (e) {
                console.error('解析topics字段失败:', e, '原始值:', post.topics);
                // 解析失败，直接使用单个主题
                topicsArray = [post.topic];
              }
            } else {
              // 不是有效的JSON格式，直接作为单个主题使用
              topicsArray = [trimmedTopics];
              console.log('非JSON格式topics:', trimmedTopics);
            }
          }
        } else if (Array.isArray(post.topics)) {
          // 已经是数组，过滤空值和非字符串值
          topicsArray = post.topics
            .map(t => t !== null && t !== undefined ? String(t) : null)
            .filter(t => t && t.trim() !== '');
          if (topicsArray.length === 0) {
            topicsArray = [post.topic];
          }
        } else if (post.topics === null || post.topics === undefined) {
          // null或undefined
          topicsArray = [post.topic];
        } else {
          // 其他类型，尝试转换为字符串
          try {
            const topicStr = String(post.topics).trim();
            topicsArray = topicStr ? [topicStr] : [post.topic];
          } catch {
            topicsArray = [post.topic];
          }
        }
      } else {
        // 没有topics字段，使用单个主题
        topicsArray = [post.topic];
      }

      // 最后确保至少有一个有效的主题
      if (!topicsArray || !Array.isArray(topicsArray) || topicsArray.length === 0) {
        topicsArray = ['企业服务'];
      }
    } catch (err) {
      // 捕获所有异常，确保不会影响整体渲染
      console.error('处理主题时发生异常:', err);
      topicsArray = [post.topic || '企业服务'];
    }

    // 将处理后的topicsArray添加到post对象
    post.topicsArray = topicsArray;

    console.log('【我的评论页】处理后的topicsArray:', topicsArray);
    return post;
  },
  formatTime(ts) {
    if (!ts) return '';
    const date = new Date(Number(ts));
    if (isNaN(date.getTime())) return '';
    const y = date.getFullYear();
    const m = (date.getMonth() + 1).toString().padStart(2, '0');
    const d = date.getDate().toString().padStart(2, '0');
    const h = date.getHours().toString().padStart(2, '0');
    const min = date.getMinutes().toString().padStart(2, '0');
    return `${y}-${m}-${d} ${h}:${min}`;
  }
});