<view class="comments-container">
  <view wx:if="{{loading}}" class="loading">加载中...</view>
  <block wx:else>
    <view wx:if="{{commentList.length === 0}}" class="empty">暂无评论记录</view>
    <block wx:for="{{commentList}}" wx:key="commentId">
      <view class="comment-item">
        <!-- 原帖信息区域 -->
        <view class="post-detail">
          <view class="post-header">
            <text class="post-header-title">原帖内容</text>
          </view>
          <view class="post-content">{{item.postContent || '无内容'}}</view>
          
          <!-- 主题标签区域 -->
          <view class="post-meta" wx:if="{{item.postInfo.region || item.postInfo.topic || item.postInfo.topicsArray}}">
            <view class="post-region" wx:if="{{item.postInfo.region}}">{{item.postInfo.regionText || item.postInfo.region}}</view>
            <!-- 多主题标签显示 -->
            <block wx:if="{{item.postInfo.topicsArray && item.postInfo.topicsArray.length > 0}}">
              <view class="post-topic" wx:for="{{item.postInfo.topicsArray}}" wx:for-item="topicItem" wx:key="*this">{{topicItem}}</view>
            </block>
            <!-- 兼容旧版单主题显示 -->
            <block wx:elif="{{item.postInfo.topic}}">
              <view class="post-topic">{{item.postInfo.topic}}</view>
            </block>
          </view>
          
          <!-- 位置信息 -->
          <view wx:if="{{item.postInfo.location}}" class="post-location">📍{{item.postInfo.location}}</view>
          
          <!-- 互动栏 -->
          <view class="interaction-bar">
            <view class="interaction-item">
              <image src="/images/icons2/未点赞.png"></image>
              <text>({{item.postInfo.likeCount || 0}})</text>
            </view>
            <view class="interaction-item">
              <image src="/images/icons2/评论.png"></image>
              <text>({{item.postInfo.commentCount || 0}})</text>
            </view>
            <view class="interaction-item">
              <image src="/images/icons2/分享.png"></image>
              <text>转发 ({{item.postInfo.shareCount || 0}})</text>
            </view>
          </view>
        </view>

        <!-- 我的评论区域 -->
        <view class="my-comment-section">
          <view class="my-comment-header">我的评论</view>
          <view class="my-comment">{{item.myComment}}</view>
          <view class="comment-time">评论时间：{{formatTime(item.commentTime)}}</view>
        </view>

        <!-- 其他用户评论区域 -->
        <view class="other-comments" wx:if="{{item.otherComments && item.otherComments.length > 0}}">
          <view class="other-title">其他用户评论：</view>
          <block wx:for="{{item.otherComments}}" wx:for-item="otherComment" wx:key="id">
            <view class="other-comment">{{otherComment.content}}</view>
          </block>
        </view>
      </view>
    </block>
  </block>
</view>