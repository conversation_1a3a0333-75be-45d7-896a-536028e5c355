.comments-container {
  padding: 8px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.comment-item {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

/* 原帖样式 */
.post-detail {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.post-header {
  margin-bottom: 8px;
}

.post-header-title {
  font-size: 14px;
  color: #999;
}

.post-content {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin-bottom: 6px;
  padding: 0;
  background: transparent;
}

/* 主题标签样式 */
.post-meta {
  display: flex;
  flex-wrap: wrap;
  margin-top: 8px;
  margin-bottom: 2px;
}

.post-region, .post-topic {
  font-size: 12px;
  color: #666666;
  background-color: #F5F5F5;
  padding: 3px 10px;
  border-radius: 10px;
  margin-right: 8px;
  margin-bottom: 5px;
  display: inline-block;
  border: 1px solid #EEEEEE;
}

.post-location {
  color: #666;
  font-size: 12px;
  margin-bottom: 5px;
}

/* 互动栏样式 */
.interaction-bar {
  display: flex;
  height: 38px;
  border-top: 1px solid #F5F5F5;
  padding-top: 6px;
  padding-bottom: 2px;
  margin-top: 2px;
}

.interaction-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.interaction-item image {
  width: 21px;
  height: 21px;
  margin-right: 4px;
}

.interaction-item text {
  font-size: 12px;
  color: #999;
}

/* 我的评论样式 */
.my-comment-section {
  margin-bottom: 10px;
}

.my-comment-header {
  font-size: 14px;
  color: #666;
  margin-bottom: 6px;
}

.my-comment {
  font-size: 14px;
  color: #1aad19;
  margin-bottom: 6px;
  background: #f8f8f8;
  padding: 8px;
  border-radius: 4px;
}

.comment-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

/* 其他评论样式 */
.other-comments {
  margin-top: 8px;
  background: #f7f7f7;
  border-radius: 4px;
  padding: 8px;
}

.other-title {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.other-comment {
  font-size: 13px;
  color: #444;
  margin-bottom: 4px;
  background: #fff;
  padding: 6px;
  border-radius: 4px;
}

.loading, .empty {
  text-align: center;
  color: #999;
  margin-top: 40px;
  padding: 20px;
}