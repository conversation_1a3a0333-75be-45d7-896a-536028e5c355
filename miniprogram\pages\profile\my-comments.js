var userApi = require('../../utils/api').userApi;

Page({
  data: {
    commentList: [],
    loading: true
  },

  onLoad: function() {
    var app = getApp();
    // 登录检查
    if (!app.checkNeedLogin(function(isLogin) {
      if (isLogin) {
        this.loadMyComments();
      }
    }.bind(this))) {
      return;
    }
    this.loadMyComments();
  },

  formatTime: function(timestamp) {
    if (!timestamp) {
      return '';
    }
    
    // 确保timestamp是数字
    timestamp = Number(timestamp);
    if (isNaN(timestamp)) {
      return '';
    }

    // 如果是秒级时间戳，转换为毫秒级
    if (timestamp < 10000000000) {
      timestamp *= 1000;
    }
    
    var date = new Date(timestamp);
    if (isNaN(date.getTime())) {
      return '';
    }
    
    var now = new Date();
    var diff = now.getTime() - date.getTime();
    
    // 如果时间差小于1分钟
    if (diff < 60000) {
      return '刚刚';
    }
    // 如果时间差小于1小时
    if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前';
    }
    // 如果时间差小于24小时
    if (diff < 86400000) {
      return Math.floor(diff / 3600000) + '小时前';
    }
    // 如果时间差小于30天
    if (diff < 2592000000) {
      return Math.floor(diff / 86400000) + '天前';
    }
    
    // 超过30天显示具体日期
    var y = date.getFullYear();
    var m = (date.getMonth() + 1).toString().padStart(2, '0');
    var d = date.getDate().toString().padStart(2, '0');
    var h = date.getHours().toString().padStart(2, '0');
    var min = date.getMinutes().toString().padStart(2, '0');
    
    return y + '年' + m + '月' + d + '日 ' + h + ':' + min;
  },

  loadMyComments: function() {
    var that = this;
    this.setData({ loading: true });
    
    console.log('开始获取我的评论数据');
    
    var token = wx.getStorageSync('token');
    var userInfo = wx.getStorageSync('userInfo');
    
    if (!token || !userInfo) {
      console.log('用户未登录，无法获取评论数据');
      this.setData({ 
        loading: false,
        commentList: []
      });
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    userApi.getMyComments().then(function(res) {
      console.log('【我的评论页】原始接口返回数据:', res);
      
      if (!res.success || !res.data) {
        console.error('获取评论列表失败: 无效的响应数据');
        that.setData({ loading: false });
        return;
      }

      var comments = res.data.map(function(item) {
        // 确保postInfo对象存在
        item.postInfo = item.postInfo || {};
        
        // 处理评论数据
        var comment = {
          commentId: item.commentId,
          postId: item.postId,
          myComment: item.myComment,
          commentTime: item.commentTime,  // 评论时间
          postContent: item.postContent,
          postInfo: {
            avatar: item.postInfo.avatar || '/images/default-avatar.png',
            nickname: item.postInfo.nickname || '用户',
            createTime: item.postInfo.createTime,  // 帖子创建时间
            commentCount: item.postInfo.commentCount || 0,
            likeCount: item.postInfo.likeCount || 0,
            shareCount: item.postInfo.shareCount || 0
          }
        };

        // 处理显示时间
        comment.formattedCommentTime = that.formatTime(comment.commentTime);
        comment.formattedPostTime = that.formatTime(comment.postInfo.createTime);

        return comment;
      });

      console.log('【我的评论页】处理后的评论数据:', comments);

      that.setData({
        commentList: comments,
        loading: false
      });
    }).catch(function(err) {
      console.error('获取评论列表失败:', err);
      that.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    });
  },

  goToPost: function(e) {
    var postId = e.currentTarget.dataset.postid;
    if (!postId) {
      return;
    }
    wx.navigateTo({
      url: '/pages/post/detail?id=' + postId
    });
  },

  processTopics: function(post) {
    if (!post) {
      return post;
    }
    
    console.log('【我的评论页】处理帖子主题：', post);
    var topicsArray = [];
    
    try {
      if (!post.topic || post.topic === '') {
        return post;
      }
      
      if (typeof post.topic === 'string') {
        try {
          topicsArray = JSON.parse(post.topic);
        } catch (err) {
          console.error('解析主题字符串失败:', err);
          topicsArray = [];
        }
      } else if (Array.isArray(post.topic)) {
        topicsArray = post.topic;
      }
      
      post.topicsArray = topicsArray;
    } catch (err) {
      console.error('处理帖子主题时出错:', err);
    }
    
    return post;
  },

  onPullDownRefresh: function() {
    this.loadMyComments();
    wx.stopPullDownRefresh();
  },

  onShareAppMessage: function() {
    return {
      title: '我的评论',
      path: '/pages/profile/my-comments'
    };
  },

  // 图片加载失败时的处理
  onImageError: function(e) {
    // 使用默认头像替换加载失败的图片
    const index = e.currentTarget.dataset.index;
    const defaultAvatar = '/images/default-avatar.png';
    
    const commentList = this.data.commentList;
    if (commentList[index]) {
      commentList[index].postInfo.avatar = defaultAvatar;
      this.setData({
        commentList: commentList
      });
    }
  }
});
