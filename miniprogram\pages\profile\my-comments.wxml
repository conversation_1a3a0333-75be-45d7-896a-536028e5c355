<view class="comments-container">
  <view wx:if="{{loading}}" class="loading">加载中...</view>
  <block wx:else>
    <view wx:if="{{commentList.length === 0}}" class="empty">暂无评论记录</view>
    <block wx:for="{{commentList}}" wx:key="commentId">
      <view class="comment-item">
        <!-- 原帖信息区域 -->
        <view class="post-section">
          <view class="post-header-row">
            <view class="post-label">原帖内容</view>
            <view class="view-detail" bindtap="goToPost" data-postid="{{item.postId}}">查看详情 ></view>
          </view>
          
          <!-- 帖子作者信息 -->
          <view class="post-author-row">
            <image class="post-author-avatar" src="{{item.postInfo.avatar}}" mode="aspectFill" binderror="onImageError"></image>
            <view class="post-author-info">
              <view class="post-author-name">{{item.postInfo.nickname}}</view>
              <view class="post-time">{{item.formattedPostTime}}</view>
            </view>
          </view>
          
          <view class="post-content">{{item.postContent}}</view>
        </view>
        
        <!-- 分隔线 -->
        <view class="divider"></view>

        <!-- 我的评论区域 -->
        <view class="my-comment-section">
          <view class="my-comment-header-row">
            <view class="my-comment-label">我的评论</view>
            <view class="comment-time">{{item.formattedCommentTime}}</view>
          </view>
          <view class="my-comment">{{item.myComment}}</view>
        </view>

        <!-- 互动栏 -->
        <view class="interaction-bar">
          <view class="interaction-item">
            <image src="/images/icons2/未点赞.png"></image>
            <text>{{item.postInfo.likeCount || 0}}</text>
          </view>
          <view class="interaction-item">
            <image src="/images/icons2/评论.png"></image>
            <text>{{item.postInfo.commentCount || 0}}</text>
          </view>
          <view class="interaction-item">
            <image src="/images/icons2/分享.png"></image>
            <text>{{item.postInfo.shareCount || 0}}</text>
          </view>
        </view>
      </view>
    </block>
  </block>
</view>
