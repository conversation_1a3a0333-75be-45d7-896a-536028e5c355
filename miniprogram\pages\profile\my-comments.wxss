.comments-container {
  padding: 10px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.comment-item {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  position: relative;
}

/* 原帖样式 */
.post-section {
  margin-bottom: 10px;
  position: relative;
}

.post-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.post-label {
  font-size: 14px;
  color: #999;
}

.view-detail {
  font-size: 12px;
  color: #1aad19;
}

/* 帖子作者信息样式 */
.post-author-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.post-author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
}

.post-author-info {
  display: flex;
  flex-direction: column;
}

.post-author-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.post-time {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.post-content {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin-bottom: 10px;
  line-height: 1.5;
  padding: 0;
  background: transparent;
}

/* 主题标签样式 */
.post-meta {
  display: flex;
  flex-wrap: wrap;
  margin-top: 8px;
  margin-bottom: 2px;
}

.post-region, .post-topic {
  font-size: 12px;
  color: #666666;
  background-color: #F5F5F5;
  padding: 3px 10px;
  border-radius: 10px;
  margin-right: 8px;
  margin-bottom: 5px;
  display: inline-block;
  border: 1px solid #EEEEEE;
}

.post-location {
  color: #666;
  font-size: 12px;
  margin-bottom: 5px;
}

/* 互动栏样式 */
.interaction-bar {
  display: flex;
  height: 38px;
  border-top: 1px solid #F5F5F5;
  padding-top: 6px;
  padding-bottom: 2px;
  margin-top: 2px;
}

.interaction-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.interaction-item image {
  width: 21px;
  height: 21px;
  margin-right: 4px;
}

.interaction-item text {
  font-size: 12px;
  color: #999;
}

/* 分隔线样式 */
.divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 10px 0;
}

/* 我的评论样式 */
.my-comment-section {
  margin-bottom: 10px;
}

.my-comment-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.my-comment-label {
  font-size: 14px;
  color: #666;
  font-weight: bold;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.my-comment {
  font-size: 14px;
  color: #1aad19;
  margin-bottom: 6px;
  line-height: 1.5;
}

/* 其他评论样式 */
.other-comments {
  margin-top: 8px;
  padding: 10px 0;
}

.other-title {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
}

.other-count {
  color: #999;
  font-weight: normal;
}

.other-comment-item {
  margin-bottom: 8px;
}

.other-comment-user {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.other-user-name {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.other-comment-time {
  font-size: 11px;
  color: #999;
}

.other-comment {
  font-size: 13px;
  color: #444;
  line-height: 1.5;
}

.loading, .empty {
  text-align: center;
  color: #999;
  margin-top: 40px;
  padding: 20px;
}
