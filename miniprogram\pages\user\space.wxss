.user-space-container {
  background: #f7f7f7;
  min-height: 100vh;
}
.user-header {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx 32rpx 24rpx;
  background: #fff;
  border-bottom: 1px solid #eee;
  min-height: unset;
}
.user-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}
.user-info {
  flex: 1;
}
.user-nickname {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
.user-id {
  font-size: 14px;
  color: #999;
  margin-top: 4px;
}
.contact-btn {
  background: #f2f2f2 !important;
  color: #222 !important;
  border-radius: 20rpx !important;
  font-size: 14px !important;
  width: 25vw !important;
  height: calc(25vw * 0.25) !important;
  line-height: calc(25vw * 0.25) !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-sizing: border-box !important;
}
.user-actions {
  display: flex;
  justify-content: space-between;
  background: #fff;
  padding: 24rpx 24rpx;
  border-bottom: 1px solid #eee;
  margin-top: 24rpx;
  min-height: unset;
}
.follow-btn, .message-btn {
  background: #ff4d4f !important;
  color: #fff !important;
  border-radius: 20rpx !important;
  font-size: 16px !important;
  width: 38vw !important;
  height: calc(38vw * 0.25) !important;
  line-height: calc(38vw * 0.25) !important;
  margin: 0 !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-sizing: border-box !important;
}
.user-posts {
  padding: 16rpx;
}
.post-item {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
}
.post-user {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}
.username {
  font-size: 15px;
  font-weight: bold;
  color: #333;
  margin-bottom: 2px;
}
.post-time {
  font-size: 12px;
  color: #999;
}
.post-content {
  margin-bottom: 12px;
  padding-left: 0;
  padding-right: 0;
}
.content-text {
  font-size: 15px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.single-image-wrapper,
.image-grid {
  width: 100%;
  padding: 0;
  margin: 0 0 8px 0;
}
.single-image {
  width: 100%;
  height: auto;
  aspect-ratio: 16/9;
  object-fit: cover;
  border-radius: 8px;
  display: block;
}
.image-grid {
  display: flex; 
  flex-wrap: wrap;
  gap: 4px;
}
.grid-image {
  flex: 1;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
  min-width: 0;
  aspect-ratio: 1/1;
}
/* 确保所有图片正常显示 */
.grid-image:last-child {
  margin-right: 0;
}
.post-video {
  width: 100%;
  display: block;
  margin: 0 auto 8px auto;
  border-radius: 8px;
  background: #000;
}
.mini-btn {
  min-width: 80rpx;
  height: 40rpx;
  line-height: 40rpx;
  font-size: 14px;
  padding: 0 16rpx;
  border-radius: 16rpx;
  margin: 0 8rpx;
}
.post-meta {
  display: flex;
  margin-top: 8px;
  margin-bottom: 2px;
}
.post-region, .post-topic {
  font-size: 12px;
  color: #666666;
  background-color: #F5F5F5;
  padding: 3px 10px;
  border-radius: 10px;
  margin-right: 8px;
  display: inline-block;
  border: 1px solid #EEEEEE;
}
/* 地区标签特殊样式 */
.post-region {
  color: #3D6DB5;
  background-color: #ECF3FC;
  border: 1px solid #D6E4F8;
}
.interaction-bar {
  display: flex;
  height: 38px;
  border-top: 1px solid #F5F5F5;
  padding-top: 6px;
  padding-bottom: 2px;
  margin-top: 2px;
}
.interaction-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.interaction-item image {
  width: 21px;
  height: 21px;
  margin-right: 4px;
}
.interaction-item text {
  font-size: 14px;
  color: #666666;
  line-height: 1;
} 